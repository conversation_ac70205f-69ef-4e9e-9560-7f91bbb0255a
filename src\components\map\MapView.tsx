import { useEffect, useCallback, useMemo, useState } from "react";
import { LocationData } from "@/types/database";
import { Card } from "../ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useNotifications } from "@/components/providers/NotificationProvider";
// Import the unified hook implementation
import { useMapInitialization } from "@/hooks/useMapInitialization.unified";
import { useMapMarkers } from "@/hooks/useMapMarkers";
import { useNativeMapFeatures } from "@/hooks/useNativeMapFeatures";
import { useOfflineMap } from "@/hooks/useOfflineMap";
import { useNativeNotifications } from "@/hooks/useNativeNotifications";
import { Capacitor } from "@capacitor/core";
import BottomSheet from "../ui/BottomSheet";
import { Download, WifiOff, Check } from "lucide-react";
import {
  isParentDashboard,
  throttle,
  calculateSmartCenter,
} from "@/utils/mapHelpers";
import MapFallback from "./MapFallback";
import MapLoadingOverlay from "./MapLoadingOverlay";
import MapErrorOverlay from "./MapErrorOverlay";
import MapControlButtons from "./MapControlButtons";
// Import iOS-specific styles
import "@/styles/ios-map.css";

interface MapViewProps {
  selectedUserId?: string;
  showControls?: boolean;
  locations: LocationData[];
  onLocationUpdate?: () => void;
  forceUpdateKey?: number;
  focusOnLatest?: boolean;
  mapStyle?:
    | 'satellite-streets-v12'
    | 'light-v11'
    | 'dark-v11'
    | 'outdoors-v12'
    | 'streets-v12';
  selectedLocation?: LocationData | null; // Para foco direto em localização específica
}

export default function MapView({
  selectedUserId,
  showControls = true,
  locations,
  onLocationUpdate,
  forceUpdateKey,
  focusOnLatest = false,
  mapStyle,
  selectedLocation,
}: MapViewProps) {
  const { toast } = useToast();
  const { showNotification } = useNotifications();
  
  // State for bottom sheet
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const [selectedLocationForSheet, setSelectedLocationForSheet] = useState<LocationData | null>(null);
  const [lastNotificationTimestamp, setLastNotificationTimestamp] = useState<number>(0);

  // Detect iOS device
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) || 
              (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  
  // Detect if we're running on a native platform
  const isNativePlatform = Capacitor.isNativePlatform();

  // Filtrar coordenadas inválidas (0,0) para evitar foco incorreto
  const validLocations = useMemo(
    () =>
      locations.filter(
        (loc) =>
          Math.abs(loc.latitude) > 0.0001 || Math.abs(loc.longitude) > 0.0001,
      ),
    [locations],
  );

  // Memoizar configurações do mapa baseadas nas localizações disponíveis
  const mapConfig = useMemo(() => {
    let currentMapStyle = mapStyle;
    if (!currentMapStyle ||
      ![
        'streets-v12',
        'satellite-streets-v12',
        'light-v11',
        'dark-v11',
        'outdoors-v12',
      ].includes(currentMapStyle)
    ) {
      currentMapStyle = 'streets-v12';
    }
    const smartCenter = calculateSmartCenter(validLocations);

    return {
      latitude: smartCenter.latitude,
      longitude: smartCenter.longitude,
      zoom: smartCenter.zoom,
      mapStyle: currentMapStyle,
      showControls,
    };
  }, [mapStyle, showControls, validLocations]);

  // Inicializar o mapa usando o hook unificado com configurações estáveis
  const { mapContainer, mapInstance, mapError, mapInitialized, isTokenValid } =
    useMapInitialization(mapConfig);

  const { markers } = useMapMarkers({
    map: mapInstance?.current,
    mapLoaded: mapInitialized,
    locations: validLocations,
    selectedUserId,
    forceUpdateKey,
  });
  
  // Add native features like haptic feedback and optimized gestures
  const {
    triggerHaptic,
    triggerSuccessHaptic,
    triggerErrorHaptic,
    isNative,
    isIOS: isNativeIOS,
    isAndroid
  } = useNativeMapFeatures({
    map: mapInstance?.current,
    mapLoaded: mapInitialized,
    onLocationUpdate
  });
  
  // Add offline map capabilities
  const {
    isOnline,
    offlineRegionStatus,
    downloadProgress,
    lastSyncTimestamp,
    downloadMapRegion,
    clearOfflineData,
    cacheLocations,
    getCachedLocations
  } = useOfflineMap({
    mapInstance,
    mapInitialized,
    locations: validLocations
  });
  
  // Initialize native notifications
  const {
    sendLocationNotification,
    sendOfflineModeNotification,
    isNative: isNativeNotifications
  } = useNativeNotifications({
    isEnabled: isNativePlatform,
    locations: validLocations,
    lastNotificationTimestamp
  });

  // Cache locations when they change and we're online
  useEffect(() => {
    if (isOnline && validLocations.length > 0 && isNativePlatform) {
      cacheLocations(validLocations);
    }
  }, [isOnline, validLocations, cacheLocations, isNativePlatform]);
  
  // Load cached locations when offline
  useEffect(() => {
    if (!isOnline && isNativePlatform) {
      getCachedLocations().then(cachedLocations => {
        if (cachedLocations.length > 0) {
          console.log('[MapView] Using cached locations:', cachedLocations.length);
          // In a real implementation, we would update the locations state here
          // Show a native-like notification
          showNotification(
            "Offline Mode",
            `Using ${cachedLocations.length} cached locations from ${new Date(lastSyncTimestamp || 0).toLocaleString()}`,
            {
              type: 'offline',
              duration: 5000,
              actions: [
                {
                  text: "OK",
                  onClick: () => console.log("Offline notification acknowledged")
                }
              ]
            }
          );
        }
      });
    }
    
    // Send notification when offline status changes
    if (isNativePlatform) {
      sendOfflineModeNotification(!isOnline);
    }
  }, [isOnline, getCachedLocations, isNativePlatform, toast, lastSyncTimestamp, sendOfflineModeNotification]);

  // Debug log para locations - LIMITADO para evitar spam
  useEffect(() => {
    if (validLocations && validLocations.length > 0 && import.meta.env.DEV) {
      console.log("[MapView] Recebidas localizações:", validLocations.length);
    }
  }, [validLocations.length]); // Apenas quando QUANTIDADE muda, não conteúdo

  // Optimized focus effect - vai direto para a localização sem passar por São Paulo
  const focusOnLocationDirectly = useCallback(
    (location: LocationData) => {
      if (mapInstance?.current && mapInitialized) {
        console.log(
          "[MapView] Focando diretamente na localização:",
          location.id,
        );

        // Usar jumpTo para ir diretamente sem animação, evitando o "pulo"
        mapInstance.current.jumpTo({
          center: [location.longitude, location.latitude],
          zoom: isParentDashboard() ? 17 : 15,
        });

        // Pequena animação suave para ajustar a visualização
        setTimeout(() => {
          if (mapInstance.current) {
            mapInstance.current.easeTo({
              zoom: 18,
              duration: 500,
            });
          }
        }, 100);
      }
    },
    [mapInstance, mapInitialized],
  );

  // Throttled focus effect - MELHORADO para evitar pulo para São Paulo
  const throttledFocusEffect = useCallback(
    throttle(() => {
      if (
        mapInstance?.current &&
        mapInitialized &&
        validLocations.length > 0 &&
        focusOnLatest
      ) {
        console.log("[MapView] Auto-foco na localização mais recente");

        const mostRecentLocation = validLocations[0];
        focusOnLocationDirectly(mostRecentLocation);

        // Show a native-like notification
        showNotification(
          "Localização atual em foco",
          `Mostrando a localização mais recente de ${new Date(mostRecentLocation.timestamp).toLocaleString()}`,
          {
            type: 'location',
            duration: 3000
          }
        );
      }
    }, 1500), // Reduzido para ser mais responsivo
    [
      mapInstance,
      mapInitialized,
      focusOnLatest,
      focusOnLocationDirectly,
      toast,
    ],
  );

  // Efeito para foco automático na localização mais recente
  const latestLocationId = validLocations[0]?.id;
  useEffect(() => {
    if (focusOnLatest && latestLocationId) {
      throttledFocusEffect();
    }
  }, [latestLocationId, focusOnLatest, throttledFocusEffect]);
  
  // Send notification for new locations
  useEffect(() => {
    if (isNativePlatform && validLocations.length > 0) {
      const latestLocation = validLocations[0];
      const locationTimestamp = new Date(latestLocation.timestamp).getTime();
      
      // Only send notification if this is a new location
      if (locationTimestamp > lastNotificationTimestamp) {
        sendLocationNotification(latestLocation)
          .then(success => {
            if (success) {
              // Update the last notification timestamp
              setLastNotificationTimestamp(locationTimestamp);
            }
          });
      }
    }
  }, [isNativePlatform, validLocations, lastNotificationTimestamp, sendLocationNotification]);

  // Efeito para foco direto em localização selecionada (histórico)
  useEffect(() => {
    if (selectedLocation && mapInstance?.current && mapInitialized) {
      console.log(
        "[MapView] Focando diretamente na localização selecionada:",
        selectedLocation.id,
      );
      focusOnLocationDirectly(selectedLocation);
    }
  }, [
    selectedLocation?.id,
    mapInstance,
    mapInitialized,
    focusOnLocationDirectly,
  ]);

  // Forçar centralização automática sempre
  const alwaysFocusOnLatest = true;

  // Se o token não for válido, mostramos fallback mais adiante

  // Apply platform-specific styling classes
  const getPlatformClasses = () => {
    let classes = "";
    
    if (isIOS) {
      classes += "ios-map-touch-optimized ios-map-no-zoom ";
    }
    
    if (isNativePlatform) {
      if (isIOS) {
        classes += "ios-native-map ";
      } else {
        classes += "android-native-map ";
      }
    }
    
    return classes;
  };

  // Get map container classes for iOS optimization
  const getMapContainerClasses = () => {
    let classes = "absolute inset-0 rounded-lg overflow-hidden ";
    
    if (isIOS) {
      classes += "map-container-ios ";
    }
    
    if (isNativePlatform) {
      classes += "native-map-container ";
    }
    
    return classes;
  };

  // Add haptic feedback to map interactions
  useEffect(() => {
    if (mapInstance?.current && mapInitialized && isNativePlatform) {
      // Add event listeners for map interactions with haptic feedback
      const map = mapInstance.current;
      
      // Define event handlers
      const handleZoomStart = () => {
        triggerHaptic('light');
      };
      
      const handleZoomEnd = () => {
        triggerHaptic('medium');
      };
      
      const handleMapClick = (e: mapboxgl.MapMouseEvent) => {
        triggerHaptic('light');
        
        // Check if we clicked on a marker
        const features = mapInstance.current?.queryRenderedFeatures(e.point, {
          layers: ['markers']
        });
        
        // If we didn't click on a marker, close the bottom sheet
        if (!features || features.length === 0) {
          if (isBottomSheetOpen) {
            setIsBottomSheetOpen(false);
          }
        }
      };
      
      // Zoom events
      map.on('zoomstart', handleZoomStart);
      map.on('zoomend', handleZoomEnd);
      
      // Click events
      map.on('click', handleMapClick);
      
      // Marker click events
      if (markers && markers.length > 0) {
        // Create a mapping between markers and their corresponding locations
        markers.forEach((marker, index) => {
          const markerElement = marker.getElement();
          
          // Store the location index as a data attribute on the marker element
          markerElement.setAttribute('data-location-index', index.toString());
          
          markerElement.addEventListener('click', (e) => {
            triggerSuccessHaptic();
            
            // Get the location index from the marker's data attribute
            const locationIndex = parseInt(markerElement.getAttribute('data-location-index') || '0', 10);
            
            // Find the location data for this marker
            if (validLocations[locationIndex]) {
              console.log("[MapView] Selected location:", validLocations[locationIndex]);
              setSelectedLocationForSheet(validLocations[locationIndex]);
              setIsBottomSheetOpen(true);
            }
          });
        });
      }
      
      return () => {
        // Clean up event listeners
        map.off('zoomstart', handleZoomStart);
        map.off('zoomend', handleZoomEnd);
        map.off('click', handleMapClick);
        
        // Clean up marker event listeners
        if (markers && markers.length > 0) {
          markers.forEach(marker => {
            const markerElement = marker.getElement();
            // Clone the element to remove all event listeners
            const newMarkerElement = markerElement.cloneNode(true);
            if (markerElement.parentNode) {
              markerElement.parentNode.replaceChild(newMarkerElement, markerElement);
            }
          });
        }
      };
    }
  }, [mapInstance, mapInitialized, isNativePlatform, markers, triggerHaptic, triggerSuccessHaptic]);

  return isTokenValid ? (
    <Card
      className={`w-full h-full map-highlight map-responsive relative ${getPlatformClasses()}`}
      data-cy="location-map-container"
    >
      <div
        ref={mapContainer}
        className={getMapContainerClasses()}
        data-cy="map-container"
      />

      <MapLoadingOverlay show={!mapInitialized && !mapError} />
      <MapErrorOverlay error={mapError} />

      <MapControlButtons
        showControls={true}
        selectedUserId={selectedUserId}
        locations={validLocations}
        mapInstance={{ current: mapInstance?.current }}
        onLocationUpdate={onLocationUpdate}
        isNative={isNativePlatform}
        triggerHaptic={triggerHaptic}
        triggerSuccessHaptic={triggerSuccessHaptic}
        triggerErrorHaptic={triggerErrorHaptic}
      />
      
      {/* Offline mode controls - only show on native platforms */}
      {isNativePlatform && (
        <div className={`absolute bottom-20 right-4 flex flex-col gap-3 ${isIOS ? 'ios-map-controls' : ''}`}>
          {/* Offline status indicator */}
          {!isOnline && (
            <div className="offline-indicator offline" title="Offline Mode">
              <WifiOff size={18} />
            </div>
          )}
          
          {/* Download map region button */}
          {isOnline && offlineRegionStatus !== 'downloading' && offlineRegionStatus !== 'ready' && (
            <button
              className={`offline-control ${isIOS ? 'ios-button' : 'android-button'}`}
              onClick={() => {
                triggerHaptic('medium');
                downloadMapRegion();
                showNotification(
                  "Downloading Map",
                  "Saving map data for offline use...",
                  {
                    type: 'offline',
                    duration: 3000
                  }
                );
              }}
              title="Save map for offline use"
            >
              <Download size={isIOS ? 18 : 20} />
            </button>
          )}
          
          {/* Download progress */}
          {offlineRegionStatus === 'downloading' && (
            <div className="offline-progress-container">
              <div className="offline-progress-bar">
                <div
                  className="offline-progress-fill"
                  style={{ width: `${downloadProgress}%` }}
                ></div>
              </div>
              <p className="text-xs text-center mt-1">{downloadProgress}%</p>
            </div>
          )}
          
          {/* Downloaded indicator */}
          {offlineRegionStatus === 'ready' && (
            <div className="offline-indicator online" title="Map available offline">
              <Check size={18} />
            </div>
          )}
        </div>
      )}
      
      {/* Native-like bottom sheet for location details */}
      {isNativePlatform && (
        <BottomSheet
          isOpen={isBottomSheetOpen}
          onClose={() => setIsBottomSheetOpen(false)}
          location={selectedLocationForSheet}
        />
      )}
    </Card>
  ) : (
    <MapFallback locations={validLocations} />
  );
}
