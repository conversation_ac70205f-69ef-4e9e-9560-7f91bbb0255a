import React from 'react';
import { useDevice } from '@/hooks/useDevice';
import { cn } from '@/lib/utils';

interface ResponsiveMapContainerProps {
  children: React.ReactNode;
  className?: string;
  minHeight?: string;
  maxHeight?: string;
}

const ResponsiveMapContainer: React.FC<ResponsiveMapContainerProps> = ({
  children,
  className,
  minHeight,
  maxHeight
}) => {
  const device = useDevice();

  // Calculate optimal height based on device and orientation
  const getContainerHeight = () => {
    if (device.type === 'mobile') {
      if (device.orientation === 'landscape') {
        return 'h-[40vh] min-h-[200px] max-h-[250px]';
      }
      // iOS específico: mais altura para evitar mapa apertado
      if (device.isIOS) {
        return 'h-[55vh] min-h-[350px]';
      }
      return 'h-[45vh] min-h-[300px]';
    }
    
    if (device.type === 'tablet') {
      if (device.orientation === 'landscape') {
        return 'h-[60vh] min-h-[350px] max-h-[500px]';
      }
      return 'h-[60vh] min-h-[400px]';
    }
    
    return 'h-[70vh] min-h-[500px]';
  };

  // Calculate padding and spacing
  const getContainerPadding = () => {
    if (device.type === 'mobile' && device.orientation === 'landscape') {
      return 'p-1';
    }
    if (device.type === 'mobile') {
      // iOS específico: padding reduzido para maximizar área do mapa
      if (device.isIOS) {
        return 'p-1';
      }
      return 'p-2';
    }
    return 'p-4';
  };

  return (
    <div 
      className={cn(
        'w-full relative rounded-lg overflow-hidden',
        getContainerHeight(),
        getContainerPadding(),
        className
      )}
      style={{
        minHeight: minHeight,
        maxHeight: maxHeight
      }}
    >
      {children}
    </div>
  );
};

export default ResponsiveMapContainer;