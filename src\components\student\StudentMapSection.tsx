
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LocationData } from '@/types/database';
import { lazy, Suspense } from 'react';
const MapView = lazy(() => import('@/components/map/MapView'));
import ResponsiveMapContainer from '@/components/layout/ResponsiveMapContainer';
import { useDevice } from '@/hooks/useDevice';
import { Loader2, AlertCircle, MapPin } from 'lucide-react';

interface StudentMapSectionProps {
  title?: string;
  selectedUserId?: string;
  locations: LocationData[];
  loading: boolean;
  error?: string | null;
  showControls?: boolean;
  userType?: 'student' | 'parent' | 'teacher';
  studentDetails?: { 
    name: string;
    email: string;
  } | null;
  senderName?: string;
  noDataContent?: React.ReactNode;
  selectedLocation?: LocationData | null;
}

const StudentMapSection: React.FC<StudentMapSectionProps> = ({
  title = "Localização",
  selectedUserId,
  locations = [],
  loading = false,
  error = null,
  showControls = true,
  userType = "parent",
  studentDetails,
  senderName,
  noDataContent,
  selectedLocation
}) => {
  const device = useDevice();
  
  // Estado local para forçar o foco na localização mais recente ou selecionada
  const [focusTimestamp, setFocusTimestamp] = React.useState(Date.now());
  
  // Efeito para forçar o foco quando uma localização específica é selecionada
  React.useEffect(() => {
    if (selectedLocation || (locations && locations.length > 0)) {
      setFocusTimestamp(Date.now());
    }
  }, [selectedLocation, locations]);
  
  console.log('[StudentMapSection] Rendering with:', {
    title,
    selectedUserId,
    locationsCount: locations?.length || 0,
    loading,
    error,
    showControls,
    userType,
    studentDetails,
    selectedLocation: !!selectedLocation
  });

  // Se uma localização específica foi selecionada, usar apenas ela, senão usar todas
  const mapLocations = selectedLocation ? [selectedLocation] : locations;

  return (
    <Card className="w-full" data-cy="student-map-section">
      <CardHeader className={`${device.type === 'mobile' && device.orientation === 'landscape' ? 'py-1 px-2' : 'pb-2'}`}>
        <CardTitle className={`flex items-center justify-between ${device.type === 'mobile' && device.orientation === 'landscape' ? 'text-sm' : ''}`}>
          <span className="flex items-center gap-2">
            <MapPin className={`${device.type === 'mobile' && device.orientation === 'landscape' ? 'h-3 w-3' : 'h-4 w-4'}`} />
            {title}
          </span>
          {loading && (
            <Loader2 className={`${device.type === 'mobile' && device.orientation === 'landscape' ? 'h-3 w-3' : 'h-4 w-4'} animate-spin text-muted-foreground`} />
          )}
        </CardTitle>
        {studentDetails && (
          <p className={`${device.type === 'mobile' && device.orientation === 'landscape' ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
            {studentDetails.name}
          </p>
        )}
      </CardHeader>
      <CardContent className="p-0 relative">
        {error ? (
          <div className="flex items-center justify-center p-12 text-destructive">
            <AlertCircle className="h-5 w-5 mr-2" />
            <p>{error}</p>
          </div>
        ) : loading ? (
          <div className="flex items-center justify-center p-12" data-cy="map-loading">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="ml-2 text-muted-foreground">Carregando localizações...</p>
          </div>
        ) : mapLocations.length === 0 ? (
          <ResponsiveMapContainer data-cy="map-container">
            {noDataContent || (
              <div className={`flex flex-col items-center justify-center ${device.type === 'mobile' && device.orientation === 'landscape' ? 'p-4' : 'p-12'} text-muted-foreground`} data-cy="no-locations-message">
                <MapPin className={`${device.type === 'mobile' && device.orientation === 'landscape' ? 'h-8 w-8 mb-2' : 'h-12 w-12 mb-4'} text-muted-foreground`} />
                <p className={`font-medium ${device.type === 'mobile' && device.orientation === 'landscape' ? 'text-sm' : ''}`}>Nenhuma localização encontrada</p>
                {userType === 'parent' && studentDetails && (
                  <p className={`${device.type === 'mobile' && device.orientation === 'landscape' ? 'text-xs mt-1' : 'text-sm mt-2'} text-center`}>
                    {studentDetails.name} ainda não compartilhou sua localização ou as localizações não estão sendo compartilhadas com responsáveis.
                  </p>
                )}
              </div>
            )}
          </ResponsiveMapContainer>
        ) : (
          <ResponsiveMapContainer data-cy="map-container">
            <Suspense fallback={<Loader2 className="h-8 w-8 animate-spin" />}>
              <MapView
                selectedUserId={selectedUserId}
                locations={mapLocations}
                showControls={showControls}
                forceUpdateKey={focusTimestamp}
                focusOnLatest={true}
              />
            </Suspense>
            {mapLocations.length > 0 && (
              <div className={`absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-lg ${device.type === 'mobile' && device.orientation === 'landscape' ? 'px-2 py-0.5 text-xs' : 'px-3 py-1 text-xs'} text-muted-foreground`}>
                {mapLocations.length} localização{mapLocations.length !== 1 ? 'ões' : ''}
              </div>
            )}
          </ResponsiveMapContainer>
        )}
      </CardContent>
    </Card>
  );
}

export default StudentMapSection;
