import React from 'react';
import StudentLocationTabs from '@/components/student/StudentLocationTabs';
import { LocationData } from '@/types/database';
import { cn } from '@/lib/utils';
import { ModernCard } from '@/components/ui/modern-card';

interface StudentLocationSectionProps {
  userId?: string;
  userName: string;
  userEmail: string;
  locations: LocationData[];
  loading: boolean;
  error: string | null;
  className?: string;
}

const StudentLocationSection: React.FC<StudentLocationSectionProps> = ({
  userId,
  userName,
  userEmail,
  locations,
  loading,
  error,
  className
}) => {
  return (
    <ModernCard variant="professional" className={cn('bg-white/60 backdrop-blur shadow-lg border border-white/30 text-foreground overflow-hidden', className)}>
      <StudentLocationTabs
        className="border-0 shadow-none bg-transparent"
        userId={userId}
        userName={userName}
        userEmail={userEmail}
        locations={locations}
        loading={loading}
        error={error}
      />
    </ModernCard>
  );
};

export default StudentLocationSection;
