import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { env, isMapboxTokenValid } from '../env';
import { useMapLocation } from './useMapLocation';
import { calculateSmartCenter } from '@/utils/mapHelpers';

/**
 * Interface consolidada para o viewport do mapa
 */
export interface MapViewport {
  latitude: number;
  longitude: number;
  zoom: number;
}

/**
 * Interface para as opções de estilo do mapa
 */
export interface MapStyleOptions {
  showControls?: boolean;
  mapStyle?: 'satellite-streets-v12' | 'light-v11' | 'dark-v11' | 'outdoors-v12' | 'streets-v12';
}

/**
 * Interface para localizações (simplificada)
 */
export interface LocationPoint {
  latitude: number;
  longitude: number;
}

/**
 * Tipo unificado para os parâmetros do hook
 */
export type UseMapInitializationParams = MapViewport & MapStyleOptions & {
  locations?: LocationPoint[];
};

/**
 * Valores default para o viewport (São Paulo)
 */
const DEFAULT_VIEWPORT: MapViewport = {
  latitude: -23.5489,
  longitude: -46.6388,
  zoom: 12
};

/**
 * Hook unificado para inicialização do mapa
 * VERSÃO OTIMIZADA PARA iOS - Correções definitivas
 */
export function useMapInitialization(params: Partial<UseMapInitializationParams> = {}) {
  // Detectar iOS IMEDIATAMENTE
  const isIOS = useMemo(() => 
    /iPad|iPhone|iPod/.test(navigator.userAgent) || 
    (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1)
  , []);

  // Log específico para iOS
  useEffect(() => {
    if (isIOS && import.meta.env.DEV) {
      console.log('[useMapInitialization] 🍎 iOS detectado - aplicando otimizações específicas');
      console.log('[useMapInitialization] 🍎 Viewport:', {
        width: window.innerWidth,
        height: window.innerHeight,
        orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
        devicePixelRatio: window.devicePixelRatio
      });
    }
  }, [isIOS]);

  // CORREÇÃO CRÍTICA: Configurar token IMEDIATAMENTE e ANTES de qualquer outra operação
  useEffect(() => {
    const token = env.MAPBOX_TOKEN_FALLBACK;
    
    if (token && token.startsWith('pk.')) {
      mapboxgl.accessToken = token;
      
      if (import.meta.env.DEV) {
        console.log('[useMapInitialization] ✅ Token Mapbox configurado:', {
          tokenPreview: `${token.substring(0, 15)}...`,
          length: token.length,
          isIOS
        });
      }
    } else {
      console.error('[useMapInitialization] ❌ Token Mapbox inválido:', {
        hasToken: !!token,
        tokenPreview: token ? `${token.substring(0, 10)}...` : 'undefined',
        startsWithPk: token?.startsWith('pk.'),
        envVars: {
          VITE_MAPBOX_ACCESS_TOKEN: !!import.meta.env.VITE_MAPBOX_ACCESS_TOKEN,
          VITE_MAPBOX_TOKEN: !!import.meta.env.VITE_MAPBOX_TOKEN
        }
      });
    }
  }, []); // SEM dependências - executar apenas uma vez

  // Memoizar parâmetros calculando centro inteligente se há localizações
  const stableParams = useMemo(() => {
    const smartCenter = params.locations && params.locations.length > 0 
      ? calculateSmartCenter(params.locations)
      : null;

    return {
      latitude: params.latitude || smartCenter?.latitude || DEFAULT_VIEWPORT.latitude,
      longitude: params.longitude || smartCenter?.longitude || DEFAULT_VIEWPORT.longitude,
      zoom: params.zoom || smartCenter?.zoom || DEFAULT_VIEWPORT.zoom,
      showControls: params.showControls ?? true,
      mapStyle: params.mapStyle || 'streets-v12'
    };
  }, [params.latitude, params.longitude, params.zoom, params.showControls, params.mapStyle, params.locations]);

  // Estado do mapa
  const [viewport, setViewport] = useState<MapViewport>({ 
    latitude: stableParams.latitude, 
    longitude: stableParams.longitude, 
    zoom: stableParams.zoom 
  });
  const [mapError, setMapError] = useState<string | null>(null);
  const [mapInitialized, setMapInitialized] = useState(false);
  const [tokenValid, setTokenValid] = useState(false);

  // Refs do mapa
  const mapContainer = useRef<HTMLDivElement>(null);
  const mapInstance = useRef<mapboxgl.Map | null>(null);
  
  // Integração com o hook de localização
  const { loading, updateLocation } = useMapLocation({
    selectedUserId: undefined
  });

  // Verificar token no carregamento - MELHORADO
  useEffect(() => {
    const isValid = isMapboxTokenValid();
    setTokenValid(isValid);
    
    if (isValid) {
      if (import.meta.env.DEV) {
        console.log('[useMapInitialization] ✅ Token validado com sucesso');
      }
    } else {
      console.error('[useMapInitialization] ❌ Falha na validação do token');
      setMapError('Token do Mapbox não configurado ou inválido');
    }
  }, []);

  // Inicializar mapa - OTIMIZADO ESPECIFICAMENTE PARA iOS
  useEffect(() => {
    if (!mapContainer.current || mapInstance.current || !tokenValid) return;

    const initMap = () => {
      if (!mapContainer.current || mapInstance.current) return;

      // Verificar novamente se o token está definido
      if (!mapboxgl.accessToken) {
        mapboxgl.accessToken = env.MAPBOX_TOKEN_FALLBACK;
      }

      // Limpar conteúdo existente
      while (mapContainer.current.firstChild) {
        mapContainer.current.removeChild(mapContainer.current.firstChild);
      }

      setTimeout(() => {
        if (!mapContainer.current || mapInstance.current) return;
        
        try {
          // Configurações BASE para o mapa
          const mapConfig: any = {
            container: mapContainer.current,
            style: `mapbox://styles/mapbox/${stableParams.mapStyle}`,
            center: [stableParams.longitude, stableParams.latitude],
            zoom: stableParams.zoom,
            attributionControl: false,
            preserveDrawingBuffer: true,
            trackResize: true,
            dragRotate: false,
            touchPitch: false,
            doubleClickZoom: true,
            touchZoomRotate: true
          };

          // OTIMIZAÇÕES ESPECÍFICAS PARA iOS
          if (isIOS) {
            console.log('[useMapInitialization] 🍎 Aplicando configurações específicas para iOS');
            
            // Performance otimizada para iOS
            mapConfig.antialias = false;
            mapConfig.refreshExpiredTiles = false;
            mapConfig.maxTileCacheSize = 50;
            mapConfig.fadeDuration = 0; // Remover fade para melhor performance
            
            // Otimizar requisições para iOS Safari
            mapConfig.transformRequest = (url: string, resourceType: string) => {
              if (resourceType === 'Tile') {
                return {
                  url: url,
                  credentials: 'omit'
                };
              }
            };
          } else {
            mapConfig.antialias = false;
            mapConfig.refreshExpiredTiles = false;
          }

          mapInstance.current = new mapboxgl.Map(mapConfig);

          mapInstance.current.on('load', () => {
            setMapInitialized(true);
            setMapError(null);
            console.log(`[useMapInitialization] ✅ Mapa carregado com sucesso ${isIOS ? 'no iOS' : 'no dispositivo'}`);
          });

          mapInstance.current.on('error', (e) => {
            console.error('[useMapInitialization] ❌ Erro no mapa:', e);
            setMapError('Erro ao carregar o mapa');
          });

          // Adicionar controles
          if (stableParams.showControls) {
            const nav = new mapboxgl.NavigationControl({ showCompass: false });
            const attribution = new mapboxgl.AttributionControl({ compact: true });
            const geolocate = new mapboxgl.GeolocateControl({
              positionOptions: {
                enableHighAccuracy: true,
                timeout: isIOS ? 15000 : 6000,  // Timeout maior para iOS
                maximumAge: isIOS ? 180000 : 600000  // Cache mais curto para iOS
              },
              trackUserLocation: true,
              showAccuracyCircle: false
            });

            mapInstance.current.addControl(nav, 'top-right');
            mapInstance.current.addControl(attribution, 'bottom-left');
            mapInstance.current.addControl(geolocate);
          }

          // OTIMIZAÇÕES ESPECÍFICAS DE CANVAS PARA iOS
          const canvas = mapInstance.current.getCanvas();
          if (canvas && isIOS) {
            console.log('[useMapInitialization] 🍎 Aplicando otimizações de canvas para iOS');
            
            // Classes CSS específicas para iOS
            canvas.classList.add('ios-map-touch-optimized', 'ios-optimized');
            
            // Propriedades de touch otimizadas
            canvas.style.touchAction = 'pan-x pan-y';
            (canvas.style as any).webkitTouchCallout = 'none';
            (canvas.style as any).webkitUserSelect = 'none';
            (canvas.style as any).webkitTransform = 'translate3d(0, 0, 0)';
            (canvas.style as any).webkitBackfaceVisibility = 'hidden';
            (canvas.style as any).webkitPerspective = '1000';
          }
        } catch (error) {
          console.error('[useMapInitialization] ❌ Falha ao criar instância do mapa:', error);
          setMapError('Falha ao inicializar o mapa');
        }
      }, isIOS ? 300 : 100); // Delay maior para iOS
    };

    // Usar requestIdleCallback se disponível
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(initMap);
    } else {
      requestAnimationFrame(initMap);
    }

    // Cleanup
    return () => {
      if (mapInstance.current) {
        mapInstance.current.remove();
        mapInstance.current = null;
        setMapInitialized(false);
      }
    };
  }, [tokenValid, isIOS, stableParams]);

  // Função para atualizar o viewport (estabilizada)
  const updateViewport = useCallback((newViewport: Partial<MapViewport>) => {
    setViewport(current => ({
      ...current,
      ...newViewport
    }));

    // Atualizar o mapa se disponível
    if (mapInstance.current) {
      // Determinar se estamos no dashboard de responsáveis para zoom mais alto
      const isParentDashboard = window.location.pathname.includes('parent-dashboard');
      const zoomLevel = isParentDashboard 
        ? Math.max(newViewport.zoom || viewport.zoom, 16)
        : (newViewport.zoom || viewport.zoom);
        
      mapInstance.current.flyTo({
        center: [
          newViewport.longitude || viewport.longitude, 
          newViewport.latitude || viewport.latitude
        ],
        zoom: zoomLevel,
        essential: true,
        speed: 0.8 // Animação mais suave
      });
    }
  }, [viewport]);

  // Função para atualizar localização (estabilizada)
  const handleUpdateLocation = useCallback(async () => {
    const location = await updateLocation(mapInstance.current);
    if (location) {
      // Determiner zoom com base no dashboard
      const isParentDashboard = window.location.pathname.includes('parent-dashboard');
      const zoomLevel = isParentDashboard ? 17 : 15;
      
      updateViewport({
        latitude: location.latitude,
        longitude: location.longitude,
        zoom: zoomLevel
      });
    }
  }, [updateLocation, updateViewport]);

  // Retornar interface unificada
  return {
    mapContainer,
    mapError,
    mapInstance,
    viewport,
    updateViewport,
    isTokenValid: tokenValid,
    mapInitialized,
    handleUpdateLocation,
    map: mapInstance.current,
    mapLoaded: mapInitialized,
    tokenValid
  };
}

// Tipo de retorno para facilitar tipagem
export type MapInitializationResult = ReturnType<typeof useMapInitialization>;
