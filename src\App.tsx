
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import { Toaster } from '@/components/ui/toaster';
import { UnifiedAuthProvider } from '@/contexts/UnifiedAuthContext';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { NotificationProvider } from '@/components/providers/NotificationProvider';
import AuthLayout from '@/layouts/AuthLayout';
import ProtectedLayout from '@/layouts/ProtectedLayout';
import Dashboard from '@/pages/Dashboard';
const ParentDashboard = lazy(() => import('@/pages/ParentDashboard'));
const StudentDashboard = lazy(() => import('@/pages/StudentDashboard'));
const StudentDashboardDemo = lazy(() => import('@/pages/StudentDashboardDemo'));
import Login from '@/pages/Login';
import Register from '@/pages/Register';
import ResetPassword from '@/pages/ResetPassword';
import ProfilePage from '@/pages/ProfilePage';
import GuardiansPage from '@/pages/GuardiansPage';
import StudentMap from '@/pages/StudentMap';
import AddStudent from '@/pages/AddStudent';
import SystemTest from '@/pages/SystemTest';
import Landing from '@/pages/Landing';
import AcceptInvitation from '@/pages/AcceptInvitation';
import ActivateAccount from '@/pages/ActivateAccount';
import HelpParent from '@/pages/HelpParent';
import HelpStudent from '@/pages/HelpStudent';
import BlankScreenDetector from '@/components/common/BlankScreenDetector';
import RefreshHandler from '@/components/common/RefreshHandler';
import { MapboxBackground } from '@/components/map/MapboxBackground';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 60 * 1000,
      gcTime: 30 * 60 * 1000,
      refetchOnWindowFocus: false,
      retry: (failureCount: number, error: any) => {
        if (error?.status === 404) return false;
        return failureCount < 3;
      },
    },
  },
});

function App() {
  return (
    <>
      <MapboxBackground />
      <RefreshHandler>
        <BlankScreenDetector timeout={15000}>
          <QueryClientProvider client={queryClient}>
            <UnifiedAuthProvider>
              <NotificationProvider>
              <Router 
                future={{
                  v7_startTransition: true,
                  v7_relativeSplatPath: true
                }}
              >
                <Routes>
                  {/* Auth routes & public pages */}
                  <Route element={<AuthLayout />}>
                    <Route path="/login" element={<Login />} />
                    <Route path="/register" element={<Register />} />
                    <Route path="/ajuda/responsavel" element={<HelpParent />} />
                    <Route path="/ajuda/estudante" element={<HelpStudent />} />
                    <Route path="/" element={<Landing />} />
                  </Route>

                  {/* Public routes for account management */}
                  <Route path="/reset-password" element={<ResetPassword />} />
                  <Route path="/accept-invitation" element={<AcceptInvitation />} />
                  <Route path="/activate-account" element={<ActivateAccount />} />

                  {/* Protected routes */}
                  <Route element={<ProtectedLayout />}>
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route
                      path="/parent-dashboard"
                      element={
                        <Suspense fallback={<div className="p-4">Carregando...</div>}>
                          <ParentDashboard />
                        </Suspense>
                      }
                    />
                    <Route
                      path="/student-dashboard"
                      element={
                        <Suspense fallback={<div className="p-4">Carregando...</div>}>
                          <StudentDashboard />
                        </Suspense>
                      }
                    />
                    <Route
                      path="/student-dashboard-demo"
                      element={
                        <Suspense fallback={<div className="p-4">Carregando...</div>}>
                          <StudentDashboardDemo />
                        </Suspense>
                      }
                    />
                    <Route path="/profile" element={<ProfilePage />} />
                    <Route path="/guardians" element={<GuardiansPage />} />
                    <Route path="/student-dashboard/guardians" element={<GuardiansPage />} />
                    <Route path="/add-student" element={<AddStudent />} />
                    <Route path="/student-map/:id?" element={<StudentMap />} />
                    <Route path="/student-dashboard/location" element={<StudentMap />} />
                    <Route path="/system-test" element={<SystemTest />} />
                  </Route>

                  {/* End protected routes */}
                </Routes>
                <Toaster />
              </Router>
              </NotificationProvider>
            </UnifiedAuthProvider>
          </QueryClientProvider>
        </BlankScreenDetector>
      </RefreshHandler>
    </>
  );
}

export default App;
