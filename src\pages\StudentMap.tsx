import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useStudentDetails } from '@/hooks/useStudentDetails';
import { useLocationData } from '@/hooks/useLocationData';
import LocationHistoryList from '@/components/student/LocationHistoryList';
import StudentMapSection from '@/components/student/StudentMapSection';
import { useDeviceType } from '@/hooks/use-mobile';
import 'mapbox-gl/dist/mapbox-gl.css';
import '@/styles/ios-map.css';

const StudentMap: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useUser();
  const navigate = useNavigate();
  const deviceType = useDeviceType();
  const [selectedStudent, setSelectedStudent] = useState<string | null>(
    id || null
  );
  
  // Detectar iOS - MELHORADO
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) || 
              (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  
  // Log específico para iOS - MELHORADO
  useEffect(() => {
    if (isIOS) {
      console.log('[StudentMap] 🍎 iOS detectado - aplicando otimizações específicas');
      console.log('[StudentMap] 🍎 Device info:', {
        deviceType,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
          orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
          devicePixelRatio: window.devicePixelRatio
        },
        safeArea: {
          top: getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-top)'),
          bottom: getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-bottom)')
        }
      });
    }
  }, [isIOS, deviceType]);
  
  // Fetch student details using our hook
  const { studentDetails } = useStudentDetails(
    selectedStudent, 
    user?.email
  );
  
  // Fetch location data using our hook
  const { locationData, loading, error } = useLocationData(
    selectedStudent || user?.id,
    user?.email,
    user?.user_metadata?.user_type
  );

  // Determine map title based on context
  const getMapTitle = () => {
    if (selectedStudent && selectedStudent !== user?.id) {
      return `Localização do ${studentDetails?.name || 'Estudante'}`;
    }
    return 'Minha Localização';
  };

  // CORREÇÃO CRÍTICA: Calcula altura otimizada especificamente para iOS
  const getMapHeight = () => {
    const device = useDeviceType();
    const orientation = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
    
    if (isIOS) {
      console.log('[StudentMap] 🍎 Calculando altura do mapa para iOS');
      
      // Alturas SIGNIFICATIVAMENTE AUMENTADAS para iOS
      if (device === 'mobile') {
        const height = orientation === 'landscape' ? '70vh' : '75vh'; // MUITO AUMENTADO
        console.log('[StudentMap] 🍎 Mobile iOS height:', height);
        return height;
      }
      if (device === 'tablet') {
        const height = orientation === 'landscape' ? '75vh' : '80vh'; // MUITO AUMENTADO
        console.log('[StudentMap] 🍎 Tablet iOS height:', height);
        return height;
      }
      return '85vh'; // MUITO AUMENTADO
    }
    
    // Valores originais para outros dispositivos
    if (device === 'mobile') {
      return orientation === 'landscape' ? '35vh' : '45vh';
    }
    if (device === 'tablet') {
      return orientation === 'landscape' ? '40vh' : '50vh';
    }
    if (device === 'laptop') {
      return orientation === 'landscape' ? '50vh' : '60vh';
    }
    return '70vh';
  };
  
  // CORREÇÃO CRÍTICA: Altura mínima muito aumentada para iOS
  const getMinMapHeight = () => {
    const device = useDeviceType();
    const orientation = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
    
    if (isIOS) {
      console.log('[StudentMap] 🍎 Calculando altura mínima do mapa para iOS');
      
      // Alturas mínimas MUITO AUMENTADAS para iOS
      if (device === 'mobile') {
        const minHeight = orientation === 'landscape' ? '400px' : '550px'; // MUITO AUMENTADO
        console.log('[StudentMap] 🍎 Mobile iOS min-height:', minHeight);
        return minHeight;
      }
      if (device === 'tablet') {
        const minHeight = orientation === 'landscape' ? '450px' : '600px'; // MUITO AUMENTADO
        console.log('[StudentMap] 🍎 Tablet iOS min-height:', minHeight);
        return minHeight;
      }
      return '650px'; // MUITO AUMENTADO
    }
    
    // Valores originais para outros dispositivos
    if (device === 'mobile') {
      return orientation === 'landscape' ? '200px' : '250px';
    }
    if (device === 'tablet') {
      return orientation === 'landscape' ? '250px' : '300px';
    }
    return '500px';
  };

  // Determine user type with proper typing
  const getUserType = (): "student" | "parent" | "teacher" => {
    const userType = user?.user_metadata?.user_type;
    if (userType === 'student' || userType === 'parent' || userType === 'teacher') {
      return userType;
    }
    return 'student'; // default fallback
  };

  // Classes CSS específicas para iOS - MELHORADO
  const getContainerClasses = () => {
    let classes = `${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'space-y-1' : 'space-y-3 md:space-y-6'} pb-16 md:pb-0`;
    
    if (isIOS) {
      classes += ' ios-map-container ios-optimized ios-smooth-scroll';
      console.log('[StudentMap] 🍎 Container classes aplicadas:', classes);
    }
    
    return classes;
  };

  const getMapCardClasses = () => {
    let classes = 'w-full map-highlight map-responsive';
    
    if (isIOS) {
      classes += ' map-container-ios ios-map-touch-optimized ios-map-no-zoom ios-optimized';
      console.log('[StudentMap] 🍎 Map card classes aplicadas:', classes);
    }
    
    return classes;
  };

  // NOVO: Adicionar data attributes para debugging
  const getMapDataAttributes = () => {
    if (isIOS && import.meta.env.DEV) {
      return {
        'data-ios': 'true',
        'data-device': deviceType,
        'data-orientation': window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
        'data-height': getMapHeight(),
        'data-min-height': getMinMapHeight()
      };
    }
    return {};
  };

  return (
    <div className={getContainerClasses()}>
      <div className={`flex ${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'flex-row items-center' : 'flex-col md:flex-row'} justify-between items-start md:items-center gap-1 md:gap-4`}>
        <div className="flex items-center gap-1 md:gap-4">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => navigate(-1)}
            className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'h-6 w-6' : 'h-7 w-7 md:h-9 md:w-9'} ${isIOS ? 'ios-map-button' : ''}`}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'text-base' : deviceType === 'mobile' ? 'text-lg' : 'text-xl md:text-2xl'} font-bold tracking-tight`}>
              {getMapTitle()}
            </h1>
            <p className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'text-xs' : 'text-xs md:text-sm'} text-gray-500`}>
              {selectedStudent && selectedStudent !== user?.id ? 
                `Localização atual e histórico de ${studentDetails?.name || 'estudante'}` :
                'Visualize e compartilhe sua localização atual'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Location history */}
      <Card>
        <CardHeader className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'py-1 px-2' : deviceType === 'mobile' ? 'py-2 px-3' : 'py-3 px-4'}`}>
          <CardTitle className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'text-xs' : deviceType === 'mobile' ? 'text-sm' : 'text-base md:text-lg'}`}>
            Histórico de Localizações
          </CardTitle>
        </CardHeader>
        <CardContent className={`${deviceType === 'mobile' && window.innerHeight < window.innerWidth ? 'px-1 py-0.5' : deviceType === 'mobile' ? 'px-2 py-1' : 'px-3 py-2 md:p-4'}`}>
          <LocationHistoryList
            locationData={locationData}
            loading={loading}
            error={error}
            userType={user?.user_metadata?.user_type}
            studentDetails={studentDetails ? { 
              id: selectedStudent || '', 
              name: studentDetails.name, 
              email: studentDetails.email 
            } : undefined}
            senderName={user?.user_metadata?.full_name}
          />
        </CardContent>
      </Card>

      {/* CORREÇÃO PRINCIPAL: Main map component com alturas otimizadas para iOS */}
      <Card 
        className={getMapCardClasses()} 
        style={{
          height: getMapHeight(),
          minHeight: getMinMapHeight()
        }}
        {...getMapDataAttributes()}
      >
        <StudentMapSection
          title={getMapTitle()}
          selectedUserId={selectedStudent || user?.id}
          showControls={!selectedStudent || selectedStudent === user?.id}
          locations={locationData}
          userType={getUserType()}
          studentDetails={studentDetails}
          senderName={user?.user_metadata?.full_name}
          loading={loading}
          noDataContent={
            <div className="text-center p-3">
              <p className="text-gray-500 text-sm">Nenhuma localização disponível</p>
            </div>
          }
        />
      </Card>
    </div>
  );
};

export default StudentMap;
