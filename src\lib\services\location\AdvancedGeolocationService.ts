
import { env } from '@/env';

export interface GeolocationResult {
  latitude: number;
  longitude: number;
  accuracy: number;
  source: 'gps_high' | 'gps_medium' | 'gps_low' | 'ip_premium' | 'ip_fallback' | 'hybrid';
  confidence: 'high' | 'medium' | 'low';
  timestamp: number;
  address?: string;
  quality_score: number; // 0-100
}

export interface GeolocationAttempt {
  position: GeolocationPosition;
  accuracy: number;
  timestamp: number;
}

export class AdvancedGeolocationService {
  private static instance: AdvancedGeolocationService;
  private watchId: number | null = null;
  private currentAttempts: GeolocationAttempt[] = [];
  private isWatching = false;

  static getInstance(): AdvancedGeolocationService {
    if (!this.instance) {
      this.instance = new AdvancedGeolocationService();
    }
    return this.instance;
  }

  /**
   * Algoritmo principal de obtenção de localização com precisão inteligente
   */
  async getHighPrecisionLocation(options?: {
    maxAttempts?: number;
    maxWatchTime?: number;
    targetAccuracy?: number;
    enableHybrid?: boolean;
  }): Promise<GeolocationResult> {
    const config = {
      maxAttempts: options?.maxAttempts || 5,
      maxWatchTime: options?.maxWatchTime || 60000, // 60 segundos
      targetAccuracy: options?.targetAccuracy || 10, // 10 metros
      enableHybrid: options?.enableHybrid ?? true,
    };

    try {
      // Fase 1: Múltiplas tentativas GPS com timeouts progressivos
      const gpsResult = await this.attemptMultipleGPS(config);
      
      if (gpsResult && gpsResult.accuracy <= config.targetAccuracy) {
        return await this.enhanceWithMapbox(gpsResult);
      }

      // Fase 2: Watch position para refinamento
      if (gpsResult && gpsResult.accuracy <= 50) {
        const refinedResult = await this.refineWithWatch(gpsResult, config);
        if (refinedResult.accuracy <= config.targetAccuracy) {
          return await this.enhanceWithMapbox(refinedResult);
        }
      }

      // Fase 3: Abordagem híbrida se habilitada
      if (config.enableHybrid && gpsResult) {
        return await this.createHybridResult(gpsResult);
      }

      // Fase 4: Fallback para IP geolocation premium
      return await this.getFallbackLocation();

    } catch (error) {
      console.warn('[AdvancedGeolocation] Erro na obtenção principal:', error);
      return await this.getFallbackLocation();
    }
  }

  /**
   * Múltiplas tentativas GPS com timeouts progressivos
   */
  private async attemptMultipleGPS(config: any): Promise<GeolocationResult | null> {
    const timeouts = [5000, 10000, 15000, 30000, 45000];
    let bestResult: GeolocationResult | null = null;

    for (let i = 0; i < Math.min(config.maxAttempts, timeouts.length); i++) {
      try {
        console.log(`[AdvancedGeolocation] Tentativa GPS ${i + 1}/${config.maxAttempts} (timeout: ${timeouts[i]}ms)`);
        
        const position = await this.getCurrentPositionWithTimeout(timeouts[i], i === 0);
        const result = this.processGPSResult(position, 'gps_high');

        if (!bestResult || result.accuracy < bestResult.accuracy) {
          bestResult = result;
          console.log(`[AdvancedGeolocation] Nova melhor precisão: ${result.accuracy}m`);
        }

        // Se atingimos a precisão alvo, retornar imediatamente
        if (result.accuracy <= config.targetAccuracy) {
          console.log(`[AdvancedGeolocation] Precisão alvo atingida: ${result.accuracy}m`);
          break;
        }

      } catch (error) {
        console.warn(`[AdvancedGeolocation] Tentativa ${i + 1} falhou:`, error);
      }

      // Pequena pausa entre tentativas para evitar spam
      if (i < config.maxAttempts - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return bestResult;
  }

  /**
   * Refinamento usando watchPosition
   */
  private async refineWithWatch(initialResult: GeolocationResult, config: any): Promise<GeolocationResult> {
    return new Promise((resolve) => {
      let bestResult = initialResult;
      let watchStartTime = Date.now();
      
      console.log('[AdvancedGeolocation] Iniciando refinamento com watchPosition');

      this.watchId = navigator.geolocation.watchPosition(
        (position) => {
          const result = this.processGPSResult(position, 'gps_medium');
          
          if (result.accuracy < bestResult.accuracy) {
            bestResult = result;
            console.log(`[AdvancedGeolocation] Refinamento: nova precisão ${result.accuracy}m`);
          }

          // Parar se atingimos precisão alvo ou tempo limite
          if (result.accuracy <= config.targetAccuracy || 
              Date.now() - watchStartTime > config.maxWatchTime) {
            this.stopWatching();
            resolve(bestResult);
          }
        },
        (error) => {
          console.warn('[AdvancedGeolocation] Erro no watchPosition:', error);
          this.stopWatching();
          resolve(bestResult);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 1000
        }
      );

      // Timeout de segurança
      setTimeout(() => {
        this.stopWatching();
        resolve(bestResult);
      }, config.maxWatchTime);
    });
  }

  /**
   * Criação de resultado híbrido GPS + IP
   */
  private async createHybridResult(gpsResult: GeolocationResult): Promise<GeolocationResult> {
    try {
      const ipResult = await this.getIPLocation();
      
      // Se IP está muito próximo do GPS (< 1km), usar GPS
      // Se estão muito distantes, pode haver erro no GPS
      const distance = this.calculateDistance(
        gpsResult.latitude, gpsResult.longitude,
        ipResult.latitude, ipResult.longitude
      );

      if (distance < 1000) { // < 1km
        return {
          ...gpsResult,
          source: 'hybrid',
          confidence: 'high',
          quality_score: Math.min(90, gpsResult.quality_score + 10)
        };
      } else if (gpsResult.accuracy > 100) {
        // GPS muito impreciso, preferir IP
        return {
          ...ipResult,
          source: 'hybrid',
          confidence: 'medium',
          quality_score: 60
        };
      } else {
        // Manter GPS mas marcar como suspeito
        return {
          ...gpsResult,
          source: 'hybrid',
          confidence: 'medium',
          quality_score: Math.max(30, gpsResult.quality_score - 20)
        };
      }
    } catch (error) {
      return gpsResult;
    }
  }

  /**
   * Enriquecer resultado com Mapbox (snap-to-road + geocoding)
   */
  private async enhanceWithMapbox(result: GeolocationResult): Promise<GeolocationResult> {
    try {
      const MAPBOX_TOKEN = env.MAPBOX_TOKEN || 'pk.eyJ1IjoidGVjaC1lZHUtbGFiIiwiYSI6ImNtN3cxaTFzNzAwdWwyanMxeHJkb3RrZjAifQ.h0g6a56viW7evC7P0c5mwQ';
      
      // Geocoding reverso para obter endereço
      const geocodeResponse = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${result.longitude},${result.latitude}.json?access_token=${MAPBOX_TOKEN}&language=pt&types=address,poi`
      );
      
      if (geocodeResponse.ok) {
        const geocodeData = await geocodeResponse.json();
        
        if (geocodeData.features && geocodeData.features.length > 0) {
          const feature = geocodeData.features[0];
          const address = feature.place_name;
          
          // Se encontrou um endereço preciso, pode ajustar ligeiramente as coordenadas
          if (feature.relevance > 0.8 && feature.geometry) {
            const [adjustedLng, adjustedLat] = feature.geometry.coordinates;
            const adjustment = this.calculateDistance(
              result.latitude, result.longitude,
              adjustedLat, adjustedLng
            );
            
            // Só ajustar se for pequeno (< 50m) para não quebrar a precisão GPS
            if (adjustment < 50) {
              return {
                ...result,
                latitude: adjustedLat,
                longitude: adjustedLng,
                address,
                quality_score: Math.min(100, result.quality_score + 15),
                source: result.source === 'gps_high' ? 'gps_high' : result.source
              };
            }
          }
          
          // Só adicionar endereço sem ajustar coordenadas
          return {
            ...result,
            address,
            quality_score: Math.min(100, result.quality_score + 5)
          };
        }
      }
    } catch (error) {
      console.warn('[AdvancedGeolocation] Erro no Mapbox enhancement:', error);
    }
    
    return result;
  }

  /**
   * Localização IP como fallback
   */
  private async getFallbackLocation(): Promise<GeolocationResult> {
    try {
      return await this.getIPLocation();
    } catch (error) {
      // Localização de emergência (São Paulo centro)
      return {
        latitude: -23.5489,
        longitude: -46.6388,
        accuracy: 10000,
        source: 'ip_fallback',
        confidence: 'low',
        timestamp: Date.now(),
        quality_score: 10,
        address: 'São Paulo, SP - Localização aproximada'
      };
    }
  }

  /**
   * IP Geolocation melhorada
   */
  private async getIPLocation(): Promise<GeolocationResult> {
    const token = env.IPINFO_TOKEN;
    
    try {
      // Tentar IPInfo primeiro (mais preciso)
      const url = token
        ? `https://ipinfo.io/json?token=${token}`
        : 'https://ipinfo.io/json';

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        const [lat, lon] = (data.loc as string).split(',');
        
        return {
          latitude: parseFloat(lat),
          longitude: parseFloat(lon),
          accuracy: token ? 1000 : 5000, // Mais preciso com token
          source: token ? 'ip_premium' : 'ip_fallback',
          confidence: token ? 'medium' : 'low',
          timestamp: Date.now(),
          quality_score: token ? 50 : 30,
          address: `${data.city}, ${data.region} - ${data.country}`
        };
      }
    } catch (error) {
      console.warn('[AdvancedGeolocation] IPInfo falhou:', error);
    }

    // Fallback para ipapi.co
    try {
      const response = await fetch('https://ipapi.co/json/');
      const data = await response.json();
      
      return {
        latitude: parseFloat(data.latitude),
        longitude: parseFloat(data.longitude),
        accuracy: 5000,
        source: 'ip_fallback',
        confidence: 'low',
        timestamp: Date.now(),
        quality_score: 25,
        address: `${data.city}, ${data.region} - ${data.country_name}`
      };
    } catch (error) {
      throw new Error('Todos os serviços de IP geolocation falharam');
    }
  }

  /**
   * Utilitários
   */
  private async getCurrentPositionWithTimeout(timeout: number, highAccuracy: boolean = true): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      const options: PositionOptions = {
        enableHighAccuracy: highAccuracy,
        timeout,
        maximumAge: highAccuracy ? 0 : 30000
      };

      navigator.geolocation.getCurrentPosition(resolve, reject, options);
    });
  }

  private processGPSResult(position: GeolocationPosition, source: GeolocationResult['source']): GeolocationResult {
    const accuracy = position.coords.accuracy || 9999;
    
    let confidence: 'high' | 'medium' | 'low';
    let quality_score: number;

    if (accuracy <= 10) {
      confidence = 'high';
      quality_score = 95;
    } else if (accuracy <= 50) {
      confidence = 'medium';
      quality_score = 75;
    } else {
      confidence = 'low';
      quality_score = 40;
    }

    return {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy,
      source,
      confidence,
      timestamp: position.timestamp,
      quality_score
    };
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Raio da Terra em metros
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δφ = (lat2-lat1) * Math.PI/180;
    const Δλ = (lon2-lon1) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  private stopWatching(): void {
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = null;
      this.isWatching = false;
    }
  }

  /**
   * Validação de qualidade da localização
   */
  validateLocation(result: GeolocationResult, previousLocation?: GeolocationResult): {
    isValid: boolean;
    issues: string[];
    correctedResult?: GeolocationResult;
  } {
    const issues: string[] = [];
    let isValid = true;

    // Verificar coordenadas básicas
    if (Math.abs(result.latitude) > 90 || Math.abs(result.longitude) > 180) {
      issues.push('Coordenadas inválidas');
      isValid = false;
    }

    // Verificar "teleporte" se há localização anterior
    if (previousLocation && result.source.startsWith('gps')) {
      const distance = this.calculateDistance(
        result.latitude, result.longitude,
        previousLocation.latitude, previousLocation.longitude
      );
      
      const timeDiff = (result.timestamp - previousLocation.timestamp) / 1000; // segundos
      const maxSpeed = 200; // km/h
      const maxDistance = (maxSpeed * 1000 * timeDiff) / 3600; // metros

      if (distance > maxDistance && timeDiff < 300) { // 5 minutos
        issues.push(`Movimento muito rápido: ${Math.round(distance)}m em ${Math.round(timeDiff)}s`);
        // Não invalidar completamente, mas reduzir confiança
        result.confidence = 'low';
        result.quality_score = Math.max(10, result.quality_score - 30);
      }
    }

    // Verificar se está no oceano (para contexto urbano brasileiro)
    const isInOcean = this.isCoordinateInOcean(result.latitude, result.longitude);
    if (isInOcean) {
      issues.push('Localização no oceano (suspeita)');
      result.confidence = 'low';
      result.quality_score = Math.max(5, result.quality_score - 50);
    }

    return { isValid, issues, correctedResult: result };
  }

  private isCoordinateInOcean(lat: number, lon: number): boolean {
    // Verificação simples para águas brasileiras
    // Atlântico próximo ao Brasil
    if (lon > -30 && lat > -35 && lat < 10) {
      return true;
    }
    return false;
  }

  /**
   * Cleanup
   */
  cleanup(): void {
    this.stopWatching();
    this.currentAttempts = [];
  }
}
